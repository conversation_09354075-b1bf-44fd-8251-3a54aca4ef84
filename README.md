# Sign Language Detector

A Python application that detects and interprets sign language gestures from real-time camera input using computer vision and OpenAI API.

## Features

- Real-time hand landmark detection using MediaPipe
- Gesture classification and translation using OpenAI API
- Support for both live camera input and file-based processing
- Text and speech output
- Optional Streamlit GUI interface

## Setup

1. Clone the repository
2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

## Usage

### Command Line Interface
```bash
# Real-time camera mode
python3 main.py --mode realtime

# Real-time with custom settings
python3 main.py --mode realtime --camera 0 --interval 1.5 --no-speech

# File processing mode
python3 main.py --mode file --input path/to/video.mp4

# Batch processing with output directory
python3 main.py --mode file --input path/to/directory --output results/
```

### Streamlit GUI
```bash
streamlit run app.py
```

### Demo Mode (No API Key Required)
```bash
python3 demo.py
```

## Project Structure

- `main.py` - Main application entry point
- `app.py` - Streamlit GUI application
- `src/` - Source code modules
  - `hand_detector.py` - Hand landmark detection
  - `gesture_extractor.py` - Gesture feature extraction
  - `openai_classifier.py` - OpenAI API integration
  - `camera_handler.py` - Real-time camera processing
  - `file_handler.py` - File input processing
  - `output_handler.py` - Text and speech output
- `tests/` - Unit tests
- `examples/` - Example videos and images

## Requirements

- Python 3.8+
- OpenAI API key (for gesture classification)
- Webcam (for real-time mode)

## Quick Start

1. **Test without API key (Demo mode):**
   ```bash
   python3 demo.py
   ```
   This will show hand detection and gesture analysis without requiring an OpenAI API key.

2. **Set up OpenAI API key:**
   ```bash
   cp .env.example .env
   # Edit .env and add: OPENAI_API_KEY=your_key_here
   ```

3. **Run real-time detection:**
   ```bash
   python3 main.py --mode realtime
   ```

4. **Process a video file:**
   ```bash
   python3 main.py --mode file --input examples/sample_video.mp4
   ```

5. **Launch web interface:**
   ```bash
   streamlit run app.py
   ```

## Features Demonstrated

✅ **Hand Landmark Detection** - Uses MediaPipe to detect 21 hand landmarks
✅ **Gesture Feature Extraction** - Processes landmarks into meaningful features
✅ **OpenAI API Integration** - Classifies gestures using AI
✅ **Real-time Processing** - Live webcam feed analysis
✅ **File Processing** - Batch analysis of images and videos
✅ **Text-to-Speech** - Audio output of detected signs
✅ **Transcript Saving** - Records detection history
✅ **Web Interface** - User-friendly Streamlit GUI
✅ **Command Line Interface** - Full-featured CLI
✅ **Demo Mode** - Test functionality without API key
