#!/usr/bin/env python3
"""
Test script to verify OpenAI API connectivity and functionality.
"""

import os
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_openai_api():
    """Test OpenAI API connectivity."""
    print("=== OpenAI API Test ===")
    
    # Get API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No OpenAI API key found in environment")
        return False
    
    print(f"✅ API key found: {api_key[:10]}...{api_key[-10:]}")
    
    try:
        # Initialize client
        client = OpenAI(api_key=api_key)
        print("✅ OpenAI client initialized")
        
        # Test simple API call
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say 'Hello, API test successful!' in JSON format with a 'message' field."}
            ],
            max_tokens=50,
            temperature=0.3
        )
        
        response_content = response.choices[0].message.content
        print(f"✅ API call successful")
        print(f"Response: {response_content}")
        
        return True
        
    except Exception as e:
        print(f"❌ API call failed: {str(e)}")
        return False

def test_sign_language_classification():
    """Test sign language classification with sample data."""
    print("\n=== Sign Language Classification Test ===")
    
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ No API key available")
        return False
    
    try:
        client = OpenAI(api_key=api_key)
        
        # Sample gesture description (like what our app would generate)
        sample_description = "Hand: Right; Extended fingers: index; Closed fingers: thumb, middle, ring, pinky; Thumb-index angle: 45.0 degrees; Thumb-index distance: 0.150; Palm orientation: 90.0 degrees; Pattern: Pointing gesture"
        
        system_prompt = """You are an expert ASL (American Sign Language) interpreter. Analyze this hand gesture and provide ONE CLEAR PREDICTION.

TASK: Identify what this gesture represents. Respond with EXACTLY ONE of these:
- A single letter (A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z)
- A single number (0, 1, 2, 3, 4, 5, 6, 7, 8, 9)
- A complete word (HELLO, HUNGRY, THANK YOU, PLEASE, SORRY, YES, NO, I, YOU, LOVE, HELP, MORE, WATER, EAT, DRINK, etc.)

PRIORITY: If this could be a word sign, choose the WORD. If it's clearly a letter/number, choose that.

COMMON ASL PATTERNS:
- Closed fist = A, S, or numbers
- Open hand = 5, HELLO, or STOP
- Pointing = 1, I, or YOU
- Pinch gesture = F, 9, or SMALL

Respond in this EXACT JSON format:
{
    "letter": "A" or null,
    "word": "HELLO" or null,
    "confidence": 0.85,
    "description": "Brief explanation"
}

Be decisive and confident in your single prediction."""

        user_prompt = f"""You are an expert ASL (American Sign Language) interpreter. Analyze this hand gesture and provide ONE CLEAR PREDICTION.

GESTURE DATA:
{sample_description}

TASK: Identify what this gesture represents. Respond with EXACTLY ONE of these:
- A single letter (A, B, C, D, E, F, G, H, I, J, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y, Z)
- A single number (0, 1, 2, 3, 4, 5, 6, 7, 8, 9)
- A complete word (HELLO, HUNGRY, THANK YOU, PLEASE, SORRY, YES, NO, I, YOU, LOVE, HELP, MORE, WATER, EAT, DRINK, etc.)

PRIORITY: If this could be a word sign, choose the WORD. If it's clearly a letter/number, choose that.

COMMON ASL PATTERNS:
- Closed fist = A, S, or numbers
- Open hand = 5, HELLO, or STOP
- Pointing = 1, I, or YOU
- Pinch gesture = F, 9, or SMALL

Respond in this EXACT JSON format:
{
    "letter": "A" or null,
    "word": "HELLO" or null,
    "confidence": 0.85,
    "description": "Brief explanation"
}

Be decisive and confident in your single prediction."""
        
        print(f"Testing with sample gesture: {sample_description}")
        
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=200,
            temperature=0.3
        )
        
        response_content = response.choices[0].message.content
        print(f"✅ Classification successful")
        print(f"Response: {response_content}")
        
        # Try to parse the JSON
        import json
        try:
            if '{' in response_content and '}' in response_content:
                json_start = response_content.find('{')
                json_end = response_content.rfind('}') + 1
                json_str = response_content[json_start:json_end]
                result = json.loads(json_str)
                print(f"✅ JSON parsing successful: {result}")
                
                # Extract prediction
                prediction = result.get('word') or result.get('letter') or "No prediction"
                print(f"🎯 Final prediction: {prediction}")
                
            else:
                print("⚠️ No JSON found in response")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Classification test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("Testing OpenAI API integration for Sign Language Detector Pro")
    print("=" * 60)
    
    # Test basic API connectivity
    api_test = test_openai_api()
    
    if api_test:
        # Test sign language classification
        classification_test = test_sign_language_classification()
        
        if classification_test:
            print("\n🎉 All tests passed! OpenAI integration is working correctly.")
        else:
            print("\n❌ Classification test failed.")
    else:
        print("\n❌ Basic API test failed.")
    
    print("=" * 60)
