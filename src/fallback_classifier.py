"""
Fallback Sign Language Classifier

This module provides basic sign language classification without requiring OpenAI API.
It uses rule-based pattern matching to identify common ASL letters and gestures.
"""

from typing import Dict, Any, Optional
import re


class FallbackSignLanguageClassifier:
    """
    Fallback classifier for basic ASL recognition using pattern matching.
    """
    
    def __init__(self):
        """Initialize the fallback classifier."""
        self.debug = True
        print("Fallback classifier initialized (no API required)")
    
    def classify_gesture(self, gesture_description: str, 
                        sign_language: str = "ASL", 
                        context: Optional[str] = None) -> Dict[str, Any]:
        """
        Classify gesture using rule-based pattern matching.
        
        Args:
            gesture_description: Description of the hand gesture
            sign_language: Sign language type (default: ASL)
            context: Additional context (optional)
            
        Returns:
            Classification result dictionary
        """
        if self.debug:
            print(f"\n=== Fallback Classification Debug ===")
            print(f"Input: {gesture_description}")
        
        try:
            # Analyze the gesture description
            result = self._analyze_gesture_patterns(gesture_description)
            result['success'] = True
            result['method'] = 'fallback_pattern_matching'
            
            if self.debug:
                print(f"Result: {result}")
                print("=== End Fallback Debug ===\n")
            
            return result
            
        except Exception as e:
            if self.debug:
                print(f"Fallback classification error: {str(e)}")
                print("=== End Fallback Debug ===\n")
            
            return {
                'success': False,
                'error': str(e),
                'letter': None,
                'word': None,
                'confidence': 0.0,
                'description': 'Fallback classification failed',
                'method': 'fallback_pattern_matching'
            }
    
    def _analyze_gesture_patterns(self, description: str) -> Dict[str, Any]:
        """
        Analyze gesture description using pattern matching rules.
        
        Args:
            description: Gesture description string
            
        Returns:
            Classification result
        """
        desc_lower = description.lower()
        
        # Extract key information
        extended_fingers = self._extract_extended_fingers(desc_lower)
        closed_fingers = self._extract_closed_fingers(desc_lower)
        patterns = self._extract_patterns(desc_lower)
        
        # Rule-based classification
        letter, word, confidence, explanation = self._apply_classification_rules(
            extended_fingers, closed_fingers, patterns, desc_lower
        )
        
        return {
            'letter': letter,
            'word': word,
            'confidence': confidence,
            'description': explanation,
            'extended_fingers': extended_fingers,
            'closed_fingers': closed_fingers,
            'patterns': patterns
        }
    
    def _extract_extended_fingers(self, description: str) -> list:
        """Extract extended fingers from description."""
        extended = []
        if 'extended fingers:' in description:
            # Find the extended fingers section
            start = description.find('extended fingers:') + len('extended fingers:')
            end = description.find(';', start)
            if end == -1:
                end = len(description)
            
            fingers_text = description[start:end].strip()
            
            # Extract individual fingers
            if 'thumb' in fingers_text:
                extended.append('thumb')
            if 'index' in fingers_text:
                extended.append('index')
            if 'middle' in fingers_text:
                extended.append('middle')
            if 'ring' in fingers_text:
                extended.append('ring')
            if 'pinky' in fingers_text:
                extended.append('pinky')
        
        return extended
    
    def _extract_closed_fingers(self, description: str) -> list:
        """Extract closed fingers from description."""
        closed = []
        if 'closed fingers:' in description:
            # Find the closed fingers section
            start = description.find('closed fingers:') + len('closed fingers:')
            end = description.find(';', start)
            if end == -1:
                end = len(description)
            
            fingers_text = description[start:end].strip()
            
            # Extract individual fingers
            if 'thumb' in fingers_text:
                closed.append('thumb')
            if 'index' in fingers_text:
                closed.append('index')
            if 'middle' in fingers_text:
                closed.append('middle')
            if 'ring' in fingers_text:
                closed.append('ring')
            if 'pinky' in fingers_text:
                closed.append('pinky')
        
        return closed
    
    def _extract_patterns(self, description: str) -> list:
        """Extract gesture patterns from description."""
        patterns = []
        
        if 'closed fist' in description:
            patterns.append('closed_fist')
        if 'open hand' in description:
            patterns.append('open_hand')
        if 'pointing gesture' in description:
            patterns.append('pointing')
        if 'pinch gesture' in description:
            patterns.append('pinch')
        
        return patterns
    
    def _apply_classification_rules(self, extended: list, closed: list, 
                                  patterns: list, description: str) -> tuple:
        """
        Apply rule-based classification logic.
        
        Returns:
            (letter, word, confidence, explanation)
        """
        
        # Rule 1: Closed fist patterns
        if 'closed_fist' in patterns or len(extended) == 0:
            if 'thumb' in extended:
                return 'A', None, 0.8, "Closed fist with thumb extended = A"
            else:
                return 'S', None, 0.7, "Closed fist = S"
        
        # Rule 2: Pointing gesture
        if 'pointing' in patterns or (len(extended) == 1 and 'index' in extended):
            return '1', None, 0.8, "Index finger pointing = 1"
        
        # Rule 3: Open hand
        if 'open_hand' in patterns or len(extended) >= 4:
            if len(extended) == 5:
                return '5', None, 0.8, "All fingers extended = 5"
            else:
                return None, 'HELLO', 0.7, "Open hand gesture = HELLO"
        
        # Rule 4: Two fingers extended
        if len(extended) == 2:
            if 'index' in extended and 'middle' in extended:
                return '2', None, 0.8, "Index and middle fingers = 2"
            elif 'index' in extended and 'thumb' in extended:
                return 'L', None, 0.7, "Index and thumb = L"
        
        # Rule 5: Three fingers extended
        if len(extended) == 3:
            if 'index' in extended and 'middle' in extended and 'ring' in extended:
                return '3', None, 0.8, "Three fingers extended = 3"
        
        # Rule 6: Four fingers extended
        if len(extended) == 4:
            if 'thumb' in closed:
                return '4', None, 0.8, "Four fingers, thumb closed = 4"
        
        # Rule 7: Pinch gesture
        if 'pinch' in patterns:
            return 'F', None, 0.7, "Pinch gesture = F"
        
        # Rule 8: Thumb only
        if len(extended) == 1 and 'thumb' in extended:
            return None, 'GOOD', 0.6, "Thumb up = GOOD"
        
        # Rule 9: Index and pinky (rock sign)
        if len(extended) == 2 and 'index' in extended and 'pinky' in extended:
            return 'I', None, 0.7, "Index and pinky = I (love you sign)"
        
        # Default: Make a reasonable guess based on finger count
        finger_count = len(extended)
        if finger_count == 0:
            return 'A', None, 0.5, f"No extended fingers, guessing A"
        elif finger_count == 1:
            return '1', None, 0.5, f"One finger extended, guessing 1"
        elif finger_count == 2:
            return '2', None, 0.5, f"Two fingers extended, guessing 2"
        elif finger_count == 3:
            return '3', None, 0.5, f"Three fingers extended, guessing 3"
        elif finger_count == 4:
            return '4', None, 0.5, f"Four fingers extended, guessing 4"
        elif finger_count == 5:
            return '5', None, 0.5, f"Five fingers extended, guessing 5"
        else:
            return None, None, 0.1, "Unable to classify gesture"
    
    def classify_sequence(self, gesture_descriptions: list, 
                         sign_language: str = "ASL") -> Dict[str, Any]:
        """
        Classify a sequence of gestures (fallback implementation).
        
        Args:
            gesture_descriptions: List of gesture descriptions
            sign_language: Sign language type
            
        Returns:
            Sequence classification result
        """
        # Simple implementation: classify each gesture and combine
        letters = []
        words = []
        
        for desc in gesture_descriptions:
            result = self.classify_gesture(desc, sign_language)
            if result.get('success'):
                if result.get('letter'):
                    letters.append(result['letter'])
                if result.get('word'):
                    words.append(result['word'])
        
        # Try to form words from letters
        if letters and not words:
            letter_sequence = ''.join(letters)
            # Check for common words
            common_words = {
                'HI': 'HI',
                'NO': 'NO',
                'OK': 'OK',
                'YES': 'YES'
            }
            
            if letter_sequence in common_words:
                words.append(common_words[letter_sequence])
        
        return {
            'success': True,
            'word': words[0] if words else None,
            'sentence': ' '.join(words) if len(words) > 1 else None,
            'confidence': 0.6,
            'individual_letters': letters,
            'method': 'fallback_sequence_matching'
        }
