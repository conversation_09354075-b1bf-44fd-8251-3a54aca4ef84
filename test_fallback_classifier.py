#!/usr/bin/env python3
"""
Test script for the fallback sign language classifier.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fallback_classifier import FallbackSignLanguageClassifier

def test_fallback_classifier():
    """Test the fallback classifier with various gesture patterns."""
    print("=== Testing Fallback Sign Language Classifier ===")
    
    classifier = FallbackSignLanguageClassifier()
    
    # Test cases with expected results
    test_cases = [
        {
            'description': "Hand: Right; Extended fingers: index; Closed fingers: thumb, middle, ring, pinky; Thumb-index angle: 45.0 degrees; Thumb-index distance: 0.150; Palm orientation: 90.0 degrees; Pattern: Pointing gesture",
            'expected': '1',
            'name': 'Pointing gesture (number 1)'
        },
        {
            'description': "Hand: Right; Extended fingers: ; Closed fingers: thumb, index, middle, ring, pinky; Thumb-index angle: 30.0 degrees; Thumb-index distance: 0.050; Palm orientation: 0.0 degrees; Pattern: Closed fist",
            'expected': 'S',
            'name': 'Closed fist (letter S)'
        },
        {
            'description': "Hand: Right; Extended fingers: thumb, index, middle, ring, pinky; Closed fingers: ; Thumb-index angle: 90.0 degrees; Thumb-index distance: 0.200; Palm orientation: 0.0 degrees; Pattern: Open hand",
            'expected': '5',
            'name': 'Open hand (number 5)'
        },
        {
            'description': "Hand: Right; Extended fingers: index, middle; Closed fingers: thumb, ring, pinky; Thumb-index angle: 60.0 degrees; Thumb-index distance: 0.120; Palm orientation: 45.0 degrees",
            'expected': '2',
            'name': 'Two fingers (number 2)'
        },
        {
            'description': "Hand: Right; Extended fingers: thumb; Closed fingers: index, middle, ring, pinky; Thumb-index angle: 90.0 degrees; Thumb-index distance: 0.180; Palm orientation: 0.0 degrees",
            'expected': 'GOOD',
            'name': 'Thumbs up (word GOOD)'
        }
    ]
    
    print(f"\nTesting {len(test_cases)} gesture patterns...\n")
    
    success_count = 0
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test {i}: {test_case['name']}")
        print(f"Input: {test_case['description'][:80]}...")
        
        result = classifier.classify_gesture(test_case['description'])
        
        if result.get('success'):
            prediction = result.get('word') or result.get('letter') or 'No prediction'
            confidence = result.get('confidence', 0.0)
            explanation = result.get('description', '')
            
            print(f"✅ Prediction: {prediction} (confidence: {confidence:.1%})")
            print(f"   Explanation: {explanation}")
            
            if prediction == test_case['expected']:
                print(f"✅ CORRECT! Expected: {test_case['expected']}")
                success_count += 1
            else:
                print(f"❌ INCORRECT. Expected: {test_case['expected']}, Got: {prediction}")
        else:
            print(f"❌ Classification failed: {result.get('error', 'Unknown error')}")
        
        print("-" * 60)
    
    print(f"\n=== Test Results ===")
    print(f"Successful predictions: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    
    if success_count == len(test_cases):
        print("🎉 All tests passed! Fallback classifier is working correctly.")
    elif success_count >= len(test_cases) * 0.8:
        print("✅ Most tests passed. Fallback classifier is working well.")
    else:
        print("⚠️ Some tests failed. Fallback classifier needs improvement.")
    
    return success_count == len(test_cases)

def test_edge_cases():
    """Test edge cases and error handling."""
    print("\n=== Testing Edge Cases ===")
    
    classifier = FallbackSignLanguageClassifier()
    
    edge_cases = [
        "",  # Empty string
        "Invalid gesture description",  # No pattern matches
        "Hand: Left; Extended fingers: index, pinky; Closed fingers: thumb, middle, ring",  # Rock sign
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\nEdge case {i}: '{case}'")
        result = classifier.classify_gesture(case)
        
        if result.get('success'):
            prediction = result.get('word') or result.get('letter') or 'No prediction'
            print(f"✅ Handled gracefully: {prediction}")
        else:
            print(f"❌ Failed: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    print("Testing Fallback Sign Language Classifier")
    print("=" * 50)
    
    # Test main functionality
    main_test_passed = test_fallback_classifier()
    
    # Test edge cases
    test_edge_cases()
    
    print("\n" + "=" * 50)
    if main_test_passed:
        print("🎉 Fallback classifier is ready for production use!")
    else:
        print("⚠️ Fallback classifier needs refinement.")
