"""
Streamlit GUI for Sign Language Detector
"""

import streamlit as st
import cv2
import numpy as np
import os
import sys
import time
import threading
from PIL import Image
import tempfile
from typing import Optional

# Add src directory to path
sys.path.append(os.path.dirname(__file__))

from src.camera_handler import <PERSON><PERSON>and<PERSON>
from src.file_handler import FileHandler
from src.output_handler import <PERSON>putHandler
from src.hand_detector import HandDetector
from src.gesture_extractor import GestureExtractor
from src.openai_classifier import SignLanguageClassifier


# Page configuration
st.set_page_config(
    page_title="Sign Language Detector",
    page_icon="🤟",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'camera_handler' not in st.session_state:
    st.session_state.camera_handler = None
if 'file_handler' not in st.session_state:
    st.session_state.file_handler = None
if 'output_handler' not in st.session_state:
    st.session_state.output_handler = None
if 'detections' not in st.session_state:
    st.session_state.detections = []
if 'transcript' not in st.session_state:
    st.session_state.transcript = []


def initialize_components():
    """Initialize the application components."""
    if st.session_state.file_handler is None:
        st.session_state.file_handler = FileHandler()
    
    if st.session_state.output_handler is None:
        st.session_state.output_handler = OutputHandler(
            enable_speech=False,  # Disable speech in web interface
            save_transcript=False  # Handle transcript in session state
        )


def setup_openai_api():
    """Setup OpenAI API key."""
    api_key = st.sidebar.text_input(
        "OpenAI API Key",
        type="password",
        help="Enter your OpenAI API key for gesture classification"
    )
    
    if api_key:
        os.environ['OPENAI_API_KEY'] = api_key
        return api_key
    
    # Check if API key is in environment
    env_key = os.getenv('OPENAI_API_KEY')
    if env_key:
        st.sidebar.success("✅ API key loaded from environment")
        return env_key
    
    st.sidebar.warning("⚠️ Please enter your OpenAI API key")
    return None


def process_uploaded_file(uploaded_file, api_key):
    """Process an uploaded file."""
    if uploaded_file is None:
        return None
    
    # Save uploaded file to temporary location
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(uploaded_file.name)[1]) as tmp_file:
        tmp_file.write(uploaded_file.getvalue())
        tmp_path = tmp_file.name
    
    try:
        # Initialize file handler and classifier
        file_handler = FileHandler()
        if api_key:
            file_handler.initialize_classifier(api_key)
        
        # Determine file type and process
        file_type = file_handler.get_file_type(tmp_path)
        
        if file_type == 'image':
            result = file_handler.process_image(tmp_path)
        elif file_type == 'video':
            # Create progress bar
            progress_bar = st.progress(0)
            
            def progress_callback(progress):
                progress_bar.progress(progress)
            
            result = file_handler.process_video(tmp_path, progress_callback)
            progress_bar.empty()
        else:
            result = {'success': False, 'error': 'Unsupported file format'}
        
        return result
    
    finally:
        # Clean up temporary file
        try:
            os.unlink(tmp_path)
        except:
            pass


def display_detection_results(result):
    """Display detection results in the UI."""
    if not result['success']:
        st.error(f"Error: {result.get('error', 'Unknown error')}")
        return
    
    file_type = result['file_type']
    
    if file_type == 'image':
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Original Image")
            # Note: We don't have the original image in the result, 
            # so we'll show the annotated one
            if 'annotated_image' in result:
                # Convert BGR to RGB for display
                annotated_rgb = cv2.cvtColor(result['annotated_image'], cv2.COLOR_BGR2RGB)
                st.image(annotated_rgb, caption="Detected Hand Landmarks")
        
        with col2:
            st.subheader("Detection Results")
            hands_detected = result['hands_detected']
            st.metric("Hands Detected", hands_detected)
            
            if result['detections']:
                for i, detection in enumerate(result['detections']):
                    with st.expander(f"Hand {i+1}: {detection['hand_label']}"):
                        st.write(f"**Confidence:** {detection['confidence']:.2%}")
                        st.write(f"**Gesture Description:** {detection['gesture_description']}")
                        
                        if 'classification' in detection and detection['classification']['success']:
                            classification = detection['classification']
                            if classification.get('letter'):
                                st.success(f"**Letter:** {classification['letter']}")
                            if classification.get('word'):
                                st.success(f"**Word:** {classification['word']}")
                            if classification.get('confidence'):
                                st.write(f"**Classification Confidence:** {classification['confidence']:.2%}")
    
    elif file_type == 'video':
        st.subheader("Video Analysis Results")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Frames", result['video_properties']['total_frames'])
        with col2:
            st.metric("Processed Frames", result['video_properties']['processed_frames'])
        with col3:
            st.metric("Hands Detected", result['total_hands_detected'])
        
        # Display frame detections
        if result['frame_detections']:
            st.subheader("Frame-by-Frame Detections")
            
            for frame_data in result['frame_detections'][:10]:  # Show first 10 frames
                with st.expander(f"Frame {frame_data['frame_number']} (t={frame_data['timestamp']:.1f}s)"):
                    for detection in frame_data['detections']:
                        st.write(f"**{detection['hand_label']} Hand**")
                        if 'classification' in detection and detection['classification']['success']:
                            classification = detection['classification']
                            if classification.get('letter'):
                                st.write(f"Letter: {classification['letter']}")
                            if classification.get('word'):
                                st.write(f"Word: {classification['word']}")
        
        # Display sequence analysis
        if result.get('sequence_analysis') and result['sequence_analysis'].get('success'):
            st.subheader("Sequence Analysis")
            sequence = result['sequence_analysis']
            
            if sequence.get('word'):
                st.success(f"**Detected Word:** {sequence['word']}")
            if sequence.get('sentence'):
                st.success(f"**Detected Sentence:** {sequence['sentence']}")
            if sequence.get('individual_letters'):
                st.write(f"**Individual Letters:** {' '.join(sequence['individual_letters'])}")


def main():
    """Main Streamlit application."""
    st.title("🤟 Sign Language Detector")
    st.markdown("Detect and interpret sign language gestures using computer vision and AI")
    
    # Initialize components
    initialize_components()
    
    # Sidebar for configuration
    st.sidebar.title("Configuration")
    
    # API Key setup
    api_key = setup_openai_api()
    
    # Mode selection
    mode = st.sidebar.selectbox(
        "Select Mode",
        ["File Upload", "Real-time Camera", "About"]
    )
    
    if mode == "File Upload":
        st.header("📁 File Upload Mode")
        st.markdown("Upload an image or video file to detect sign language gestures")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a file",
            type=['jpg', 'jpeg', 'png', 'bmp', 'mp4', 'avi', 'mov', 'mkv'],
            help="Supported formats: Images (JPG, PNG, BMP) and Videos (MP4, AVI, MOV, MKV)"
        )
        
        if uploaded_file is not None:
            st.success(f"File uploaded: {uploaded_file.name}")
            
            # Process button
            if st.button("🔍 Analyze File", type="primary"):
                if not api_key:
                    st.error("Please provide an OpenAI API key to analyze gestures")
                else:
                    with st.spinner("Processing file..."):
                        result = process_uploaded_file(uploaded_file, api_key)
                        
                        if result:
                            display_detection_results(result)
    
    elif mode == "Real-time Camera":
        st.header("📹 Real-time Camera Mode")
        st.markdown("Use your webcam for real-time sign language detection")
        
        if not api_key:
            st.error("Please provide an OpenAI API key for real-time detection")
        else:
            st.info("Real-time camera mode is not fully supported in the web interface. Please use the command-line version for real-time detection.")
            st.code("python main.py --mode realtime", language="bash")
            
            # Simple camera test
            if st.button("Test Camera"):
                try:
                    cap = cv2.VideoCapture(0)
                    if cap.isOpened():
                        ret, frame = cap.read()
                        if ret:
                            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                            st.image(frame_rgb, caption="Camera Test - Frame Captured")
                        cap.release()
                    else:
                        st.error("Could not access camera")
                except Exception as e:
                    st.error(f"Camera error: {e}")
    
    elif mode == "About":
        st.header("ℹ️ About Sign Language Detector")
        
        st.markdown("""
        ### Features
        - **Real-time Detection**: Live webcam feed processing
        - **File Processing**: Analyze images and videos
        - **AI-Powered Classification**: Uses OpenAI API for gesture interpretation
        - **Multiple Sign Languages**: Support for ASL and other sign languages
        - **Text-to-Speech**: Audio output for detected gestures
        - **Transcript Saving**: Keep records of detected signs
        
        ### How It Works
        1. **Hand Detection**: Uses MediaPipe to detect hand landmarks
        2. **Feature Extraction**: Processes landmarks into gesture features
        3. **AI Classification**: OpenAI API interprets gestures into letters/words
        4. **Output**: Displays results and optionally speaks them
        
        ### Usage
        
        #### Command Line Interface
        ```bash
        # Real-time mode
        python main.py --mode realtime
        
        # File processing mode
        python main.py --mode file --input path/to/video.mp4
        
        # Batch processing
        python main.py --mode file --input path/to/directory --output results/
        ```
        
        #### Web Interface
        Use this Streamlit interface for easy file upload and processing.
        
        ### Requirements
        - Python 3.8+
        - OpenAI API key
        - Webcam (for real-time mode)
        
        ### Supported Formats
        - **Images**: JPG, PNG, BMP, TIFF, WebP
        - **Videos**: MP4, AVI, MOV, MKV, WMV, FLV
        """)
        
        # System information
        st.subheader("System Information")
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Python Version:**", sys.version.split()[0])
            st.write("**OpenCV Version:**", cv2.__version__)
        
        with col2:
            st.write("**Streamlit Version:**", st.__version__)
            api_status = "✅ Configured" if api_key else "❌ Not configured"
            st.write("**OpenAI API:**", api_status)
    
    # Footer
    st.markdown("---")
    st.markdown(
        "Built with ❤️ using MediaPipe, OpenAI, and Streamlit | "
        "[GitHub](https://github.com/your-repo) | "
        "[Documentation](https://your-docs.com)"
    )


if __name__ == "__main__":
    main()
