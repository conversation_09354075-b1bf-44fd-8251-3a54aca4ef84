#!/usr/bin/env python3
"""
Demo script for Sign Language Detector
Shows basic functionality without requiring OpenAI API key
"""

import cv2
import numpy as np
import sys
import os
import time

# Add src directory to path
sys.path.append(os.path.dirname(__file__))

from src.hand_detector import HandDetector
from src.gesture_extractor import GestureExtractor


def demo_hand_detection():
    """Demo hand detection with webcam."""
    print("Starting hand detection demo...")
    print("Press 'q' to quit, 'c' to capture frame")
    
    # Initialize components
    detector = HandDetector()
    extractor = GestureExtractor()
    
    # Initialize camera
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        print("Error: Could not open camera")
        return
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip frame for mirror effect
            frame = cv2.flip(frame, 1)
            
            # Detect hands
            annotated_frame, hand_landmarks = detector.detect_hands(frame)
            
            # Add text overlay
            cv2.putText(annotated_frame, f"Hands detected: {len(hand_landmarks)}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # Display gesture information
            y_offset = 60
            for i, hand_data in enumerate(hand_landmarks):
                text = f"Hand {i+1}: {hand_data['label']} ({hand_data['confidence']:.2f})"
                cv2.putText(annotated_frame, text, (10, y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
                y_offset += 30
                
                # Extract and display gesture features
                try:
                    description = extractor.create_gesture_description(hand_data)
                    print(f"Gesture: {description}")
                except Exception as e:
                    print(f"Error extracting gesture: {e}")
            
            # Display frame
            cv2.imshow('Sign Language Detector Demo', annotated_frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('c'):
                # Capture frame
                filename = f"capture_{int(time.time())}.jpg"
                cv2.imwrite(filename, annotated_frame)
                print(f"Frame captured: {filename}")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
        detector.cleanup()


def demo_image_processing():
    """Demo image processing with sample images."""
    print("Image processing demo...")
    
    # Create a sample image with hand-like shapes
    image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Draw hand-like shapes
    # Hand 1 (left side)
    cv2.circle(image, (150, 300), 50, (255, 255, 255), -1)  # Palm
    for i in range(5):
        x = 130 + i * 8
        y = 250 - i * 5
        cv2.rectangle(image, (x, y), (x + 6, y + 50), (255, 255, 255), -1)  # Fingers
    
    # Hand 2 (right side)
    cv2.circle(image, (450, 300), 50, (255, 255, 255), -1)  # Palm
    for i in range(5):
        x = 430 + i * 8
        y = 250 - (4-i) * 5  # Different finger pattern
        cv2.rectangle(image, (x, y), (x + 6, y + 50), (255, 255, 255), -1)  # Fingers
    
    # Initialize detector
    detector = HandDetector(static_image_mode=True)
    extractor = GestureExtractor()
    
    try:
        # Process image
        annotated_image, hand_landmarks = detector.detect_hands(image)
        
        print(f"Hands detected in sample image: {len(hand_landmarks)}")
        
        for i, hand_data in enumerate(hand_landmarks):
            print(f"\nHand {i+1}:")
            print(f"  Label: {hand_data['label']}")
            print(f"  Confidence: {hand_data['confidence']:.2f}")
            
            # Extract gesture features
            try:
                description = extractor.create_gesture_description(hand_data)
                print(f"  Description: {description}")
                
                # Extract feature vector
                features = extractor.extract_features_vector(hand_data)
                print(f"  Feature vector shape: {features.shape}")
                print(f"  Sample features: {features[:5]}")
                
            except Exception as e:
                print(f"  Error: {e}")
        
        # Save result
        cv2.imwrite('demo_result.jpg', annotated_image)
        print(f"\nResult saved as 'demo_result.jpg'")
        
        # Display result
        cv2.imshow('Demo Result', annotated_image)
        print("Press any key to close the image window...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    
    finally:
        detector.cleanup()


def demo_gesture_analysis():
    """Demo gesture analysis features."""
    print("Gesture analysis demo...")
    
    # Create mock hand landmarks for different gestures
    gestures = {
        "Open Hand": {
            'label': 'Right',
            'landmarks': [
                {'x': 0.5, 'y': 0.5, 'z': 0.0},  # Wrist
                {'x': 0.4, 'y': 0.4, 'z': 0.1},  # Thumb base
                {'x': 0.35, 'y': 0.35, 'z': 0.15},  # Thumb mid
                {'x': 0.3, 'y': 0.3, 'z': 0.2},  # Thumb IP
                {'x': 0.25, 'y': 0.25, 'z': 0.25},  # Thumb tip (extended)
                {'x': 0.45, 'y': 0.4, 'z': 0.1},  # Index base
                {'x': 0.45, 'y': 0.3, 'z': 0.15},  # Index PIP
                {'x': 0.45, 'y': 0.25, 'z': 0.2},  # Index DIP
                {'x': 0.45, 'y': 0.2, 'z': 0.25},  # Index tip (extended)
                {'x': 0.5, 'y': 0.4, 'z': 0.1},   # Middle base
                {'x': 0.5, 'y': 0.3, 'z': 0.15},  # Middle PIP
                {'x': 0.5, 'y': 0.25, 'z': 0.2},  # Middle DIP
                {'x': 0.5, 'y': 0.2, 'z': 0.25},  # Middle tip (extended)
                {'x': 0.55, 'y': 0.4, 'z': 0.1},  # Ring base
                {'x': 0.55, 'y': 0.3, 'z': 0.15}, # Ring PIP
                {'x': 0.55, 'y': 0.25, 'z': 0.2}, # Ring DIP
                {'x': 0.55, 'y': 0.2, 'z': 0.25}, # Ring tip (extended)
                {'x': 0.6, 'y': 0.4, 'z': 0.1},   # Pinky base
                {'x': 0.6, 'y': 0.3, 'z': 0.15},  # Pinky PIP
                {'x': 0.6, 'y': 0.25, 'z': 0.2},  # Pinky DIP
                {'x': 0.6, 'y': 0.2, 'z': 0.25},  # Pinky tip (extended)
            ],
            'confidence': 0.95
        },
        
        "Closed Fist": {
            'label': 'Right',
            'landmarks': [
                {'x': 0.5, 'y': 0.5, 'z': 0.0},   # Wrist
                {'x': 0.4, 'y': 0.4, 'z': 0.1},   # Thumb base
                {'x': 0.35, 'y': 0.35, 'z': 0.15}, # Thumb mid
                {'x': 0.3, 'y': 0.3, 'z': 0.2},   # Thumb IP
                {'x': 0.4, 'y': 0.45, 'z': 0.25}, # Thumb tip (closed)
                {'x': 0.45, 'y': 0.4, 'z': 0.1},  # Index base
                {'x': 0.45, 'y': 0.45, 'z': 0.15}, # Index PIP
                {'x': 0.45, 'y': 0.5, 'z': 0.2},  # Index DIP
                {'x': 0.45, 'y': 0.55, 'z': 0.25}, # Index tip (closed)
                {'x': 0.5, 'y': 0.4, 'z': 0.1},   # Middle base
                {'x': 0.5, 'y': 0.45, 'z': 0.15}, # Middle PIP
                {'x': 0.5, 'y': 0.5, 'z': 0.2},   # Middle DIP
                {'x': 0.5, 'y': 0.55, 'z': 0.25}, # Middle tip (closed)
                {'x': 0.55, 'y': 0.4, 'z': 0.1},  # Ring base
                {'x': 0.55, 'y': 0.45, 'z': 0.15}, # Ring PIP
                {'x': 0.55, 'y': 0.5, 'z': 0.2},  # Ring DIP
                {'x': 0.55, 'y': 0.55, 'z': 0.25}, # Ring tip (closed)
                {'x': 0.6, 'y': 0.4, 'z': 0.1},   # Pinky base
                {'x': 0.6, 'y': 0.45, 'z': 0.15}, # Pinky PIP
                {'x': 0.6, 'y': 0.5, 'z': 0.2},   # Pinky DIP
                {'x': 0.6, 'y': 0.55, 'z': 0.25}, # Pinky tip (closed)
            ],
            'confidence': 0.92
        }
    }
    
    extractor = GestureExtractor()
    
    for gesture_name, hand_data in gestures.items():
        print(f"\nAnalyzing {gesture_name}:")
        print("-" * 30)
        
        try:
            # Normalize landmarks
            normalized = extractor.normalize_landmarks(hand_data)
            print(f"Normalized landmarks: {len(normalized)} points")
            
            # Extract finger states
            finger_states = extractor.extract_finger_states(normalized)
            print("Finger states:")
            for finger, extended in finger_states.items():
                status = "Extended" if extended else "Closed"
                print(f"  {finger.capitalize()}: {status}")
            
            # Calculate angles and distances
            angles = extractor.calculate_angles(normalized)
            distances = extractor.extract_distances(normalized)
            
            print(f"Thumb-Index angle: {angles['thumb_index_angle']:.1f}°")
            print(f"Palm orientation: {angles['palm_orientation']:.1f}°")
            print(f"Thumb-Index distance: {distances['thumb_index_distance']:.3f}")
            
            # Create description
            description = extractor.create_gesture_description(hand_data)
            print(f"Description: {description}")
            
            # Extract feature vector
            features = extractor.extract_features_vector(hand_data)
            print(f"Feature vector: {features.shape} - {features[:3]}...")
            
        except Exception as e:
            print(f"Error analyzing {gesture_name}: {e}")


def main():
    """Main demo function."""
    print("Sign Language Detector Demo")
    print("=" * 40)
    
    while True:
        print("\nSelect demo mode:")
        print("1. Hand Detection (Webcam)")
        print("2. Image Processing")
        print("3. Gesture Analysis")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            demo_hand_detection()
        elif choice == '2':
            demo_image_processing()
        elif choice == '3':
            demo_gesture_analysis()
        elif choice == '4':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")


if __name__ == '__main__':
    main()
