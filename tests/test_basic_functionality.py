#!/usr/bin/env python3
"""
Basic functionality tests for Sign Language Detector
"""

import unittest
import sys
import os
import numpy as np
import cv2

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.hand_detector import HandDetector
from src.gesture_extractor import GestureExtractor
from src.openai_classifier import SignLanguageClassifier
from src.output_handler import OutputHandler


class TestHandDetector(unittest.TestCase):
    """Test cases for HandDetector class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.detector = HandDetector()
    
    def test_initialization(self):
        """Test HandDetector initialization."""
        self.assertIsNotNone(self.detector.hands)
        self.assertIsNotNone(self.detector.mp_hands)
        self.assertEqual(self.detector.max_num_hands, 2)
    
    def test_detect_hands_empty_image(self):
        """Test hand detection on empty image."""
        # Create a black image
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        annotated_image, hand_landmarks = self.detector.detect_hands(image)
        
        self.assertIsNotNone(annotated_image)
        self.assertEqual(len(hand_landmarks), 0)
    
    def test_detect_hands_with_noise(self):
        """Test hand detection on noisy image."""
        # Create a random noise image
        image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        annotated_image, hand_landmarks = self.detector.detect_hands(image)
        
        self.assertIsNotNone(annotated_image)
        self.assertIsInstance(hand_landmarks, list)
    
    def tearDown(self):
        """Clean up after tests."""
        self.detector.cleanup()


class TestGestureExtractor(unittest.TestCase):
    """Test cases for GestureExtractor class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.extractor = GestureExtractor()
    
    def test_initialization(self):
        """Test GestureExtractor initialization."""
        self.assertEqual(len(self.extractor.finger_tips), 5)
        self.assertEqual(len(self.extractor.finger_bases), 5)
    
    def test_normalize_landmarks(self):
        """Test landmark normalization."""
        # Create mock hand landmarks
        mock_landmarks = {
            'label': 'Right',
            'landmarks': [
                {'x': 0.5, 'y': 0.5, 'z': 0.0},  # Wrist
                {'x': 0.6, 'y': 0.4, 'z': 0.1},  # Thumb CMC
                {'x': 0.7, 'y': 0.3, 'z': 0.2},  # Thumb MCP
                {'x': 0.8, 'y': 0.2, 'z': 0.3},  # Thumb IP
                {'x': 0.9, 'y': 0.1, 'z': 0.4},  # Thumb tip
            ] + [{'x': 0.5 + i*0.1, 'y': 0.5 + i*0.1, 'z': i*0.1} for i in range(16)]  # Other landmarks
        }
        
        normalized = self.extractor.normalize_landmarks(mock_landmarks)
        
        self.assertEqual(len(normalized), 21)  # 21 hand landmarks
        # Wrist should be at origin after normalization
        self.assertAlmostEqual(normalized[0]['x'], 0.0, places=5)
        self.assertAlmostEqual(normalized[0]['y'], 0.0, places=5)
    
    def test_extract_finger_states(self):
        """Test finger state extraction."""
        # Create normalized landmarks for open hand
        normalized_landmarks = []
        for i in range(21):
            normalized_landmarks.append({
                'x': i * 0.1,
                'y': -i * 0.1 if i in [4, 8, 12, 16, 20] else i * 0.1,  # Tips above PIPs
                'z': 0.0
            })
        
        finger_states = self.extractor.extract_finger_states(normalized_landmarks)
        
        self.assertIn('thumb', finger_states)
        self.assertIn('index', finger_states)
        self.assertIn('middle', finger_states)
        self.assertIn('ring', finger_states)
        self.assertIn('pinky', finger_states)
    
    def test_create_gesture_description(self):
        """Test gesture description creation."""
        mock_landmarks = {
            'label': 'Right',
            'landmarks': [{'x': 0.5 + i*0.01, 'y': 0.5 + i*0.01, 'z': i*0.01} for i in range(21)]
        }
        
        description = self.extractor.create_gesture_description(mock_landmarks)
        
        self.assertIsInstance(description, str)
        self.assertIn('Hand: Right', description)


class TestOutputHandler(unittest.TestCase):
    """Test cases for OutputHandler class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.output_handler = OutputHandler(
            enable_speech=False,  # Disable speech for testing
            save_transcript=False  # Disable file saving for testing
        )
    
    def test_initialization(self):
        """Test OutputHandler initialization."""
        self.assertFalse(self.output_handler.enable_speech)
        self.assertFalse(self.output_handler.save_transcript)
        self.assertEqual(len(self.output_handler.transcript), 0)
    
    def test_display_detection(self):
        """Test detection display."""
        mock_detection = {
            'hand_label': 'Right',
            'classification': {
                'success': True,
                'letter': 'A',
                'confidence': 0.85,
                'description': 'Closed fist with thumb to the side'
            }
        }
        
        # This should not raise an exception
        self.output_handler.display_detection(mock_detection, speak=False)
    
    def test_transcript_summary(self):
        """Test transcript summary."""
        summary = self.output_handler.get_transcript_summary()
        
        self.assertIn('total_entries', summary)
        self.assertIn('detections', summary)
        self.assertIn('sequences', summary)
        self.assertEqual(summary['total_entries'], 0)
    
    def tearDown(self):
        """Clean up after tests."""
        self.output_handler.cleanup()


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete pipeline."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.detector = HandDetector()
        self.extractor = GestureExtractor()
        self.output_handler = OutputHandler(enable_speech=False, save_transcript=False)
    
    def test_complete_pipeline_no_hands(self):
        """Test complete pipeline with no hands detected."""
        # Create empty image
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        
        # Detect hands
        annotated_image, hand_landmarks = self.detector.detect_hands(image)
        
        # Should have no detections
        self.assertEqual(len(hand_landmarks), 0)
    
    def test_gesture_extraction_pipeline(self):
        """Test gesture extraction pipeline with mock data."""
        # Create mock hand landmarks
        mock_landmarks = {
            'label': 'Right',
            'landmarks': [{'x': 0.5 + i*0.01, 'y': 0.5 + i*0.01, 'z': i*0.01} for i in range(21)],
            'confidence': 0.9
        }
        
        # Extract gesture description
        description = self.extractor.create_gesture_description(mock_landmarks)
        
        # Create mock classification
        mock_classification = {
            'success': True,
            'letter': 'A',
            'confidence': 0.85
        }
        
        # Create detection object
        detection = {
            'hand_label': mock_landmarks['label'],
            'gesture_description': description,
            'classification': mock_classification
        }
        
        # Display detection
        self.output_handler.display_detection(detection, speak=False)
        
        # Verify transcript was updated
        summary = self.output_handler.get_transcript_summary()
        self.assertEqual(summary['detections'], 1)
    
    def tearDown(self):
        """Clean up after tests."""
        self.detector.cleanup()
        self.output_handler.cleanup()


def create_test_image_with_hand():
    """Create a simple test image with a hand-like shape."""
    image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # Draw a simple hand-like shape (rectangle for palm, lines for fingers)
    # Palm
    cv2.rectangle(image, (300, 300), (400, 400), (255, 255, 255), -1)
    
    # Fingers
    for i in range(5):
        x = 310 + i * 20
        cv2.rectangle(image, (x, 250), (x + 15, 300), (255, 255, 255), -1)
    
    return image


def run_manual_test():
    """Run a manual test with a simple hand image."""
    print("Running manual test...")
    
    # Create test image
    test_image = create_test_image_with_hand()
    
    # Initialize detector
    detector = HandDetector()
    extractor = GestureExtractor()
    
    try:
        # Detect hands
        annotated_image, hand_landmarks = detector.detect_hands(test_image)
        
        print(f"Hands detected: {len(hand_landmarks)}")
        
        if hand_landmarks:
            for i, hand_data in enumerate(hand_landmarks):
                print(f"Hand {i+1}: {hand_data['label']}")
                print(f"Confidence: {hand_data['confidence']:.2f}")
                
                # Extract gesture description
                description = extractor.create_gesture_description(hand_data)
                print(f"Gesture description: {description}")
        
        # Save test image
        cv2.imwrite('test_output.jpg', annotated_image)
        print("Test image saved as 'test_output.jpg'")
        
    finally:
        detector.cleanup()


if __name__ == '__main__':
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50)
    
    # Run manual test
    run_manual_test()
